import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useConversions } from '../hooks/useConversionQuery';
import {
  ConversionQueryParams,
  ConversionStatus,
  ConversionListItemDto,
} from '../types/conversion.types';
import ConversionDetailForm from '../components/ConversionDetailForm';

/**
 * Trang quản lý chuyển đổi
 */
const ConversionPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho table
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // State cho SlideInForm
  const [selectedConversionId, setSelectedConversionId] = useState<number | null>(null);
  const [isDetailFormVisible, setIsDetailFormVisible] = useState(false);

  // Hàm mở form chi tiết
  const handleViewDetail = useCallback((id: number) => {
    setSelectedConversionId(id);
    setIsDetailFormVisible(true);
  }, []);

  // Hàm đóng form chi tiết
  const handleCloseDetail = useCallback(() => {
    setIsDetailFormVisible(false);
    setSelectedConversionId(null);
  }, []);

  // Định nghĩa cột cho bảng
  const columns: TableColumn<ConversionListItemDto>[] = useMemo(
    () => [
      {
        key: 'id',
        title: t('conversion.id'),
        dataIndex: 'id',
        sortable: true,
      },
      {
        key: 'convertCustomerId',
        title: t('conversion.customerId'),
        dataIndex: 'convertCustomerId',
        sortable: true,
      },
      {
        key: 'userId',
        title: t('conversion.userId'),
        dataIndex: 'userId',
        sortable: true,
      },
      {
        key: 'conversionType',
        title: t('conversion.type'),
        dataIndex: 'conversionType',
        sortable: true,
      },
      {
        key: 'source',
        title: t('conversion.source'),
        dataIndex: 'source',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('common.createdAt'),
        dataIndex: 'createdAt',
        render: (value: unknown) => {
          if (!value) return '-';
          try {
            const timestamp = value as string;
            // Kiểm tra xem timestamp có phải là số hợp lệ không
            if (/^\d+$/.test(timestamp)) {
              // Là timestamp dạng số
              const date = new Date(Number(timestamp));
              if (!isNaN(date.getTime())) {
                return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
              }
            }
            // Nếu không phải timestamp dạng số, thử parse như date string
            const date = new Date(timestamp);
            if (!isNaN(date.getTime())) {
              return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
            }
            return String(value);
          } catch {
            return String(value);
          }
        },
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common.actions'),
        render: (_, record) => {
          return (
            <div className="flex space-x-2">
              <Tooltip content={t('common.view')}>
                <IconCard
                  icon="eye"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleViewDetail(Number(record.id))}
                />
              </Tooltip>
            </div>
          );
        },
      },
    ],
    [t, handleViewDetail]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ConversionQueryParams => {
    const queryParams: ConversionQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as ConversionStatus;
    }

    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.startDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.endDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ConversionListItemDto, ConversionQueryParams>({
      columns,
      filterOptions: [
        { id: 'all', label: t('common.all'), icon: 'list', value: 'all' },
        {
          id: 'completed',
          label: t('conversion.status.completed'),
          icon: 'check',
          value: ConversionStatus.COMPLETED,
        },
        {
          id: 'pending',
          label: t('conversion.status.pending'),
          icon: 'clock',
          value: ConversionStatus.PENDING,
        },
        {
          id: 'failed',
          label: t('conversion.status.failed'),
          icon: 'x',
          value: ConversionStatus.FAILED,
        },
      ],
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách chuyển đổi
  const { data: conversionsData, isLoading } = useConversions(dataTable.queryParams);

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback(
    (column: string | null, order: SortOrder | null) => {
      // Nếu column hoặc order là null, reset sort
      if (column === null || order === null) {
        dataTable.tableData.handleSortChange(null, null);
        return;
      }

      dataTable.tableData.handleSortChange(column, order as SortOrder);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [ConversionStatus.COMPLETED]: t('conversion.status.completed'),
      [ConversionStatus.PENDING]: t('conversion.status.pending'),
      [ConversionStatus.FAILED]: t('conversion.status.failed'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho chi tiết conversion */}
      <SlideInForm isVisible={isDetailFormVisible}>
        {selectedConversionId && (
          <ConversionDetailForm id={selectedConversionId} onClose={handleCloseDetail} />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={conversionsData?.result.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: conversionsData?.result.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: conversionsData?.result.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default ConversionPage;
