/* CSS cho animation form */
.slide-form-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 600px;
  z-index: 1000;
  pointer-events: none;
}

.slide-form-container > div {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: var(--background);
  border-left: 1px solid var(--border);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
}

/* Animation khi form xuất hiện */
.slide-form-enter {
  transform: translateX(100%);
  opacity: 0;
}

.slide-form-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-in-out;
}

/* Animation khi form biến mất */
.slide-form-exit {
  transform: translateX(0);
  opacity: 1;
}

.slide-form-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 300ms ease-in-out;
}
