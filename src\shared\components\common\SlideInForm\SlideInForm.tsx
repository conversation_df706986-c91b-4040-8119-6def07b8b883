import React, { ReactNode, useRef } from 'react';
import { CSSTransition } from 'react-transition-group';
import './SlideInForm.css';

interface SlideInFormProps {
  children: ReactNode;
  isVisible: boolean;
  className?: string;
  timeout?: number;
}

/**
 * Component hiển thị form với animation trư<PERSON>t từ phải sang trái
 * @param props Props của component
 * @returns Component form với animation
 */
const SlideInForm: React.FC<SlideInFormProps> = ({
  children,
  isVisible,
  className = '',
  timeout = 300,
}) => {
  const nodeRef = useRef<HTMLDivElement>(null);

  return (
    <div className="slide-form-container">
      <CSSTransition
        in={isVisible}
        timeout={timeout}
        classNames="slide-form"
        unmountOnExit
        nodeRef={nodeRef}
      >
        <div ref={nodeRef} className={className}>{children}</div>
      </CSSTransition>
    </div>
  );
};

export default SlideInForm;
